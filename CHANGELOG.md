## [Unreleased] - 2025-09-13
### ♻️ Refactored
- **Receipt Duplication:**
  - Changed duplication strategy in **AddReceipt**:
    - Old: `DuplicateReceiptUseCase` created a new receipt directly in the database.
    - New: duplication opens the AddReceipt screen in **DUPLICATE mode** with pre-filled data.
      The user can review, adjust, and explicitly save it, instead of automatic DB creation.
  - Introduced `ScreenMode` (EDIT / DUPLICATE) to unify screen initialization.
  - Removed `DuplicateReceiptUseCase` and related UI state (`DuplicateState`, events).
  - Added `LoadReceiptUseCase` + `ReceiptToFormDataMapper` for preparing data in Edit/Duplicate modes.
  - UI updates:
    - `ReceiptListScreen` provides "Duplikuj" option via `ReceiptCard`.
    - `AddReceiptTopBar` shows correct title depending on mode.

### 🚀 Impact
- Duplication no longer creates database entries automatically.
- User now has full control: a duplicated receipt opens as a new editable form,
  and saving is an explicit action.

## [Unreleased] - 2025-09-10
przeniesc logike exportu i duplikacji do listsy paragonow (feature:receiptlist)
1. export musi pobierac dane z db
2. duplikacja otwiera ekran dodawania NOWEGO paragonu z prefilled danymi z mozliwoscia zapisania, a nie edycja i aktualizacja

done:
1. wykomentowalem funkcje exportu z addreceipt, modul sie kompiluje
2. przenioslem caly kod related do exportu do ExportManager.kt
3. przenislem kod zwiazany z file manager w module add receipt do pliku FileManager.kt (interfejs)
4. caly katalog csv (export manager i file manager) przeniesiony do modulu receiptList
### ♻️ Refactored
- **CSV Export:**
  - CSV export functionality moved out of **AddReceipt** and into the **ReceiptList** module:
    1. Export logic in AddReceipt temporarily commented to keep compilation
    2. Export code centralized into `ExportManager.kt`
    3. File handling logic extracted into `FileManager.kt` (interface)
    4. Entire `csv/` package relocated under `receiptlist`
  - Removed from AddReceipt:
    - `ExportState` in `AddReceiptUiState`
    - `ShareReceiptCsv` event
    - Export-related logic in `AddReceiptViewModel`
    - "Udostępnij CSV" menu item in `AddReceiptContent`
    - Export state handling in `StatusIndicator` and `ReceiptImageSection`
    - Koin bindings for `FileManager` and `ExportManager`
  - New placeholders (`FileManager.kt`, `ExportManager.kt`) created in `receiptlist/csv/` with commented code for future implementation

### 🚀 Impact
- Simplifies AddReceipt by removing unrelated CSV export logic
- Prepares ReceiptList module as the new home for CSV export functionality


## [Unreleased] - 2025-09-07
### ♻️ Refactored
- **AddReceipt (State Management):**
  - `AddReceiptUiState` restructured:
    - Grouped into `ReceiptFormData`, `AsyncOperationsState`, `ScreenContext`, `UiState`
    - Added `LoadState` for tracking receipt loading
    - Removed error states from `SaveState`, `DeleteState`, `DuplicateState` (now via `UserMessage`)
    - `isAnyOperationInProgress` computed inside `AsyncOperationsState`
  - Introduced **UserMessage** system:
    - Flexible display of messages (`ERROR`, `SUCCESS`, `INFO`, `WARNING`)
    - Replaces `UiError` in many places
    - Can be dismissed via `ClearUserMessage` event
- **ViewModel:**
  - Updated to use grouped state updates (`updateFormData`, `updateOperations`, etc.)
  - Errors now set via `setUserMessage`
  - `clearState()` preserves categories/types
- **UI Components:**
  - `ReceiptImageSection`, `StatusIndicator`, `ProductCard` updated to read from new state groups
  - `StatusIndicator` prioritizes `UserMessage` and includes close button
  - `LaunchedEffect`s updated to track `operations.save` / `operations.delete`
- **Mappers & Reducers:**
  - `AddReceiptMapper` reads from `formData`
  - `OpenAiResponseMapper` updates `formData` & `operations`
  - `ScanStateReducer` now updates `operations.scan`
- **Other:**
  - `ExportManager` updates `operations.export`
  - `AddReceiptEvent` now includes `ClearUserMessage`

### 🚀 Impact
- More structured and maintainable state
- Unified error and info messaging system
- Clearer UI bindings with grouped state properties

## [Unreleased] - 2025-09-07
### ♻️ Refactored
- **AddReceipt (Architecture):**
  - Removed obsolete Manager classes:
    - `OcrManager`
    - `OpenAiManager` (renamed to `OpenAiResponseMapper`)
    - `ProductManager`
    - `ReceiptManager`
    - `UiStateManager`
  - Centralized business logic in:
    - `AddReceiptViewModel` (UI state management, OCR results, product logic, AI parsing via mappers and use cases)
    - Core domain use cases (`SaveReceiptUseCase`, `UpdateReceiptUseCase`, `DuplicateReceiptUseCase`, `DeleteReceiptUseCase`, `GetReceiptsUseCase`)
  - Introduced new mappers:
    - `AddReceiptMapper` (UI → domain model)
    - `OpenAiResponseMapper` (AI responses → UI state)
  - `ImageFileManager` moved to `core/domain/service`
  - `ScanStateReducer` updated to accept state update functions
  - `ExportManager` updated to receive functions directly from ViewModel
  - Dependency Injection updated:
    - Removed Manager bindings
    - Registered new mappers and updated use cases with dependencies
- **Debugging:**
  - Added logging (`println`) for AI parsing flow in `AIRepositoryImpl`, `ParseReceiptUseCase`, `OpenAiServiceImpl`, `AddReceiptViewModel`

### 🚀 Impact
- Eliminates Manager layer, reducing indirection
- Improves **Clean Architecture** adherence
- Easier to test and maintain business logic
- Clearer separation of concerns between **UI**, **ViewModel**, and **domain use cases**


## [Unreleased] - 2025-08-31
### ♻️ Refactored
- **AddReceipt (Scanning):**
  - Introduced `ScanState` sealed class (`Idle`, `PendingAutoScan`, `Scanning`, `Completed`) for explicit scan flow management.
  - Added `ScanStateReducer` to encapsulate scan state transitions.
  - Updated `AddReceiptViewModel` with new scan events and refactored scanning logic.
  - Updated `AddReceiptContent` to dispatch scan requests and observe state via `LaunchedEffect`.
  - Improved handling of recovered scans and automatic scan retries.
  - `OcrManager` and `UiStateManager` now reset `scanState` correctly.
  - `KoinModule` updated to provide `ScanStateReducer`.

### 🐛 Fixed
- **Issue #1:**
  - Fixed unreliable navigation and scanning behavior caused by scattered boolean flags.
  - Refactoring to `ScanState` ensures consistent scanning flow and proper recovery from process death.


## [Unreleased] - 2025-08-30
### ♻️ Refactored
- **AddReceipt flow:**
  - Decoupled `ReceiptManager` from `UiStateManager` — now `AddReceiptViewModel` manages state updates.
  - Simplified `ReceiptManager`: removed direct UI state updates, now returns `Result<Receipt>` for load/save/duplicate operations.
  - `AddReceiptViewModel` updated to handle UI state transitions, duplication, deletion, and save logic.
  - `AddReceiptUiState` extended with `DuplicateState` for duplication tracking.
  - `AddReceiptContent`: navigation on successful save/delete now handled via `LaunchedEffect`.

- **UI Components:**
  - `StatusIndicator` supports duplication status.
  - `ReceiptImageSection` forwards `duplicateState`.

- **Core/Domain/Data:**
  - `ReceiptRepository` and `DuplicateReceiptUseCase` now return duplicated `Receipt`.
  - `ReceiptRepositoryImpl` adjusted accordingly.

- **Dependency Injection:**
  - `ReceiptManager` provided without `UiStateManager` dependency.

### 🐛 Fixed
- **Issue #5:**
  - Fixed navigation not returning to previous screen after saving a receipt.
  - Cause: `stateManager.clearState()` was called inside `ReceiptManager.saveReceipt` before returning success result


## [Unreleased] - 2025-08-17
### 📝 Documentation
- **Git Workflow Documentation:**
  - Added `README_DEV.md` (Model B):
    - Short-lived feature branches for each new feature, fix, or improvement
    - Merge back to main upon completion
    - Step-by-step guide for branching, committing, stashing, updating from main, merging, and cleanup
    - Flow diagrams and git cherry-pick quick reference
  - Added `README_DEV_ALTERNATIVE.md` (Model A):
    - Long-lived, module-specific branches
    - Periodic merges into main
    - Guidance on using git cherry-pick
    - Lists advantages and disadvantages
    - Tips for mixing Model A and B where needed
### 🚀 Impact
- Provides clear instructions for two Git workflow models
- Helps developers choose and follow consistent branching strategies
- Supports both solo work and collaborative development


## [Unreleased] - 2025-08-17
### ♻️ Refactored
- **Dashboard Budget Calculations:**
  - **DashboardViewModel:**
    - Injects `GetBudgetForWeekUseCase` to fetch actual weekly budgets.
    - `updateBudgetCalculations` now uses actual weekly budget or defaults if unavailable.
    - Daily spending and adaptive budgets now calculated dynamically based on fetched budget.
    - Default filter is `DateFilter.Today` and `selectedDate` initialized appropriately.
    - Improved logging for budget computation steps.
  - **BudgetManager:**
    - Constants renamed: `WEEKLY_BUDGET` → `DEFAULT_WEEKLY_BUDGET`, `DAILY_BASE_BUDGET` → `DEFAULT_DAILY_BASE_BUDGET`.
    - Budget calculation methods updated to accept actual weekly/daily budgets.
    - Predictive and custom filter budget logic adjusted to use dynamic values.
  - **CurrentWeekBudget UI:**
    - Removed unused/commented code and redundant UI elements.
  - **Dependency Injection:**
    - `GetBudgetForWeekUseCase` provided to `DashboardViewModel`.
### 🚀 Impact
- Dashboard now reflects **user-set weekly budgets** dynamically.
- Daily and predictive budget calculations are **accurate and flexible**.
- UI cleaned from redundant or unused budget display elements.



## [Unreleased] - 2025-08-16
### ✨ Added
- **Budget Feature (CRUD + history tracking):**
  - **Core Domain:**
    - Added `Budget` and `BudgetHistory` models.
    - Introduced `BudgetChangeType` enum (`CREATED`, `UPDATED`, `DELETED`).
    - Defined `BudgetRepository` interface.
    - Added use cases:
      - `GetBudgetForWeekUseCase`
      - `SetBudgetForWeekUseCase`
      - `UpdateBudgetUseCase`
      - `DeleteBudgetUseCase`
      - `GetBudgetHistoryUseCase`
  - **Data Layer:**
    - Implemented `BudgetRepositoryImpl` with Room.
    - Created `BudgetEntity` and `BudgetHistoryEntity` with foreign key (CASCADE delete).
    - Added `BudgetDao` with transactional methods.
    - Updated `AppDatabase` (version 5) to include budget entities and DAO.
  - **Feature/Budget:**
    - `BudgetViewModel` now handles CRUD operations, week selection, error messages, and history loading.
    - Integrated use case calls (previously commented).
  - **Dependency Injection:**
    - Added Koin modules for `BudgetRepository` and use cases.
    - Updated DI to inject `BudgetViewModel`.
  - **Build Config:**
    - Incremented Room DB version to **5**.

### 🚀 Impact
- Users can now **set, view, update, and delete weekly budgets**.
- Added **budget history tracking** for auditing changes.
- Data is stored **persistently in Room database**.


## [Unreleased] - 2025-08-15
### ✨ Added
- **Budget Feature (initial setup)**:
  - Created new `feature/budget` module with:
    - `BudgetScreen.kt`: Basic UI for weekly budgets with week selector, budget info display, and history placeholder.
    - `BudgetViewModel.kt`: ViewModel with placeholder event handling (load, edit, save, delete, history).
    - `BudgetUiState.kt`: UI state model for budget screen.
    - `BudgetEvent.kt`: Events for budget management.
    - `WeekData.kt`: Data model for week metadata.
    - `build.gradle.kts`: Module configuration.
  - Added core domain models:
    - `Budget.kt`: Weekly budget model.
    - `BudgetHistory.kt`: Budget change history model and `BudgetChangeType` enum.
  - Navigation updates:
    - Added `Screen.Budget` to shared navigation.
    - Integrated `BudgetScreen` into `NavGraph.kt`.
  - Home screen integration:
    - "Budżet tygodniowy" drawer item in `CustomDrawer.kt`.
    - ShoppingBag icon for budget in `DrawerItem.kt`.
  - DI updates:
    - Commented out Koin module setup for budget feature.
    - Added `feature:budget` dependency in DI and navigation modules.
  - Settings updates:
    - Included `:feature:budget` in `settings.gradle.kts`.

### 🚧 Notes
- Budget functionality is not yet implemented — current commit provides scaffolding, UI shells, and navigation hooks.


## [Unreleased] - 2025-08-10
### ✨ Added
- **Dashboard**
  - **Previous Week Summary**:
    - Toggle button to switch between bar chart view (`DailySpendingsBars`) and grid view (`DailySpendingsGrid`).
    - Smooth transitions between views with `AnimatedContent` and animated toggle icon.
    - Animated bar chart with staggered height animations (spring effect).
    - Animated grid view with fade-in and scale-up for each item.
    - Improved layout clarity and visual styling.
    - Horizontal divider between `CurrentWeekBudget` and `PreviousWeekSummary`.
    - `DonutChart` moved to appear after summary section.
  - **BACKLOG.md** file for tracking future tasks and issues.

### 🛠 Changed
- **CurrentWeekBudget**
  - Defaulted `isAdaptiveBudgetActive` to `true`.
  - Replaced `AnimatedVisibility` for spending recommendation with `AnimatedContent`.
  - Shows difference between `DAILY_BASE_BUDGET` and `spent` when adaptive budget is active.
- **BudgetManager**
  - Made `DAILY_BASE_BUDGET` a public `const val`.
- **DashboardUiState**
  - `isAdaptiveBudgetActive` now defaults to `true`.
 

## [Unreleased] - 2025-08-10
### ✨ Added
- **Dashboard**
  - **Previous Week Summary**:
    - Displays date range, total spending vs. budget, and a visual progress bar.
    - Compact daily spending grid with per-day cards.
    - Quick stats: highest spending day and number of days over budget.
    - Trend icons and progress indicators for visual insights.
  - **UI Enhancements** (Current Week Budget):
    - Added budget type labels/icons (adaptive, base).
    - Smart spending recommendations based on adaptive budget logic.
    - Smooth animations for recommendation appearance/disappearance.

- **Core Models**
  - New `DaySpending` and `PreviousWeekSummary` data classes for weekly summaries.

- **Testing**
  - Added comprehensive unit and integration tests:
    - **BudgetManagerTest** (15 cases) – adaptive, retrospective, predictive, emergency budgets.
    - **DashboardViewModelTest** (7 cases) – day selection, summary loading.
    - **AdaptiveBudgetIntegrationTest** (7 cases) – end-to-end budget scenarios.
    - **PreviousWeekSummaryTest** (8 cases) – summary data and display validation.
  - Added `README.md` documenting test coverage and scenarios.

### 🛠 Changed
- `DashboardViewModel` extended with logic to fetch, process, and display previous week summary.
- `CurrentWeekBudget` composable updated with new parameters and visual enhancements.

### 🧩 Build
- Added `kotlinx-coroutines-test` and `mockk` as test dependencies in `feature/dashboard/build.gradle.kts`.


## [Unreleased] - 2025-08-09
### ✨ Added
- **Dashboard**
  - **Adaptive daily budget**:
    - Calculates today's budget dynamically based on spending earlier in the week.
    - Supports adaptive budgets for past, current, and upcoming days.
    - Introduces budget types: Current Adaptive, Retrospective, Predictive.
    - Provides **spending recommendations** based on time of day and budget usage:
      - Messages such as "Na dobrej drodze!", "Wydajesz za szybko", "Możesz wydać więcej".
    - Displays variance from planned spending (above/below target).
  - **UI Enhancements**:
    - `CurrentWeekBudget` now shows:
      - Smart budget indicator.
      - Color-coded progress bar for normal, near limit, and exceeded states.
      - Animated recommendation card.
      - Adaptive budget type labels.

- **Core Utilities**
  - New date/time helpers in `DateUtils`:
    - `getCurrentTimeOfDay`, `getCurrentMonthDays`, `getLastMonthDays`, `getYesterdayStart`, `getYesterdayEnd`.
    - `isSameDay`, `isSingleDayRange`.
  - Improved KDoc documentation.

### 🛠 Changed
- `DashboardViewModel` refactored to centralize budget calculations and trigger adaptive budget updates on relevant events.
- `BudgetManager` extended with adaptive budget logic, budget progress tracking, and spending recommendation generation.


## [Unreleased] - 2025-08-09
### ✨ Added
- **Dashboard**
  - New **budget tracking** with `BudgetManager`:
    - Supports "This Week", "Today", and single-day selections.
    - Displays spent, remaining, and overspent amounts.
    - Progress bar with yellow (normal) and red (overflow) states.
  - `CurrentWeekBudget` component replaces `CurrentWeekCalendar` for a more integrated budget view.
  - Extracted `DonutChart` and `HorizontalBarChartItem` into separate components; reduced donut size to 150dp.
- **Receipts**
  - Full **delete receipt** functionality:
    - Added `DeleteReceiptUseCase`.
    - Integrated delete flow with `ReceiptManager`, `AddReceiptViewModel`, and UI.
    - Added confirmation dialog for deletion.
- **Shared**
  - Added new date filters: `PreviousWeek` and `ThisAndPreviousWeek`.

### 🛠 Changed
- **Dashboard UI**
  - Replaced `Divider` with `HorizontalDivider` for consistency.
  - Improved day selection logic in `DashboardViewModel`.

### 🗑 Removed
- Replaced `CurrentWeekCalendar` in Dashboard with `CurrentWeekBudget`.


## [Unreleased] - 2025-08-06
### ✨ Added
- **Receipts**
  - Added ability to duplicate receipts:
    - New option in overflow menu ("Zduplikuj") to create a copy with a new ID and updated metadata.
    - The copied receipt has reset image, adjusted name (to indicate duplication), and fresh save dates.

- **ViewModel / UI**
  - Added `DuplicateReceipt` and `DeleteReceipt` events to `AddReceiptEvent`.
  - Added confirmation dialogs for clearing and deleting receipts.
  - UI updates in `AddReceiptContent` for new menu options.

- **Domain / Data**
  - Introduced `DuplicateReceiptUseCase`.
  - Implemented `duplicateReceipt` in `ReceiptRepositoryImpl`.

### 🛠 Changed
- **Dependency Injection**
  - Registered `DuplicateReceiptUseCase` in DI (Koin).


## [Unreleased] - 2025-08-06
### ✨ Added
- **Dashboard**
  - Introduced `CurrentWeekCalendar` component for selecting a day in the current week.
  - New date selection logic via `DashboardViewModel.onDaySelected()` with support for resetting selection.
  - Integrated new calendar UI into `DashboardScreen` (replacing previous `DateFilterComponent`).

- **Utilities**
  - Added calendar utilities in `core/utils/DateUtils.kt`:
    - Week number calculation.
    - Day and month data for the current week.


## [Unreleased] - 2025-08-03
### 🛠 Changed
- **Android Document Scanner**
  - Introduced `DocumentScannerState` singleton to manage scanner-related state.
  - `AndroidDocumentScanner` now uses `DocumentScannerState` instead of static variables, improving testability and reducing memory leak risk.
  - File save operations moved to IO dispatcher for better threading.
  - Removed unused debug print statements in `MainActivity`.

  
## [Unreleased] - 2025-08-03
### ✨ Added
- **Image File Management**
  - New `ImageFileManager` interface for unified receipt image handling.
  - Android implementation (`ReceiptImageManager`) manages temp/final files and cleans up old temp files.
  - iOS implementation added as a no-op (file lifecycle handled by iOS).
  - Integrated with `ReceiptManager` to finalize image file paths when saving receipts.

- **Simplified Process Death Recovery (Android)**
  - Refactored `AndroidDocumentScanner` to use `ReceiptImageManager` for file handling.
  - Added `CheckForRecoveredScan` composable to restore scanned image after process death and trigger OCR.

### 🛠 Changed
- Removed `DocumentScannerRecoveryManager` and redundant SharedPreferences state handling.
- Removed `AddReceiptEvent.RecoverFromProcessDeath`; recovery now handled fully in UI layer.
- Updated DI to provide `ImageFileManager`.



## [Unreleased] - 2025-08-03
### ✨ Added
- **Scan Process Death Recovery (Android)**
  - Document scanning now survives process death caused by the system.
  - Scan state is persisted in SharedPreferences and restored on app restart.
  - Recovered scan results are processed automatically with OCR.

### 🛠 Changed
- `AndroidDocumentScanner` and `MainActivity` updated to use process death recovery logic.
- `AddReceiptViewModel` and `AddReceiptContent` updated to support restored scans.


## [Unreleased] - 2025-08-03
### ♻️ Refactor
- **Android Document Scanning**
  - Migrated from deprecated `startActivityForResult/onActivityResult` to **`ActivityResultLauncher`** API.
  - `AndroidDocumentScanner` now uses a provided launcher via `setLauncher()`.
  - `MainActivity` handles activity results using the new `registerForActivityResult` approach.

## [Unreleased] - 2025-08-02
### ✨ Features
- **Navigation / Home Screen**
  - Added a **Floating Action Button (FAB)** with a dropdown menu for **"Add receipt"** and **"Scan receipt"** actions on the `HomeGraphScreen`.
  - `AddReceiptScreen` is now a **standalone navigable destination**, improving navigation clarity.

### ♻️ Changes
- Removed **"Add Receipt"** from the bottom bar destinations.
- Updated `HomeGraphScreen` to accept navigation callbacks (`navigateToAddReceipt`, `navigateToAddReceiptWithScan`, `navigateToEditReceipt`).
- Refactored `NavGraph`:
  - Added dedicated routes for `Screen.AddReceipt`, `Screen.AddReceiptWithScan`, and `Screen.EditReceipt`.
  - These routes now pass `navigateBack` and `startWithScan` to `AddReceiptScreen`.

### 🖼️ UI/UX
- `AddReceiptContent` now has a **back arrow in the TopAppBar**.
- Added automatic scanner launch when `startWithScan` is `true`.

### 📝 Other
- Removed debug print statements in `DashboardViewModel`.

## [Unreleased] - 2025-07-29
### ✨ Features
- **Dashboard Visualization**:
  - Added `DonutChart` composable to display category spending as a donut chart with interactive selection.
  - Added `HorizontalBarChartItem` composable to display spending per category as horizontal bars with percentages and sums.
  - `DashboardViewModel` now fetches categories via `GetTypesAndCategoriesUseCase` and calculates `totalSpending`.

### ♻️ Changes
- Updated `DashboardUiState`:
  - Added `totalSpending` and `availableCategories`.
  - `categorySpendings` now uses `Long` for sums instead of `String`.
- Updated layout of `DashboardScreen` to a `LazyColumn` and removed old `CategorySum` composable.

### 🗂 Dependency Injection
- Provided `GetTypesAndCategoriesUseCase` to `DashboardViewModel` in Koin module.

### 📝 Other
- Minor documentation update in `ReceiptDao.kt`.


## [Unreleased] - 2025-07-28
### ✨ Features
- **Dashboard**:
  - Added new `DashboardScreen` showing spending per category with date filtering.
  - `DashboardViewModel` handles receipt fetching, filter state, and spending calculations.
  - Introduced new data classes `ReceiptData` and `CategorySpending` for UI display.

### ♻️ Refactoring / Improvements
- Moved `DateFilterComponent`, `DateFilter`, and `DateFilterUtils` to `shared/component` for reuse across features.
- `ReceiptListScreen` and `ReceiptListViewModel` updated to use shared date filter components.

### 🏠 Navigation
- `HomeGraphScreen`:
  - Start destination changed from `Screen.ReceiptList` to `Screen.Dashboard`.
  - Added `DashboardScreen` to navigation graph.

### 🗂 Dependency Injection
- Added `DashboardViewModel` to Koin modules.
- Updated `di/build.gradle.kts` to include `feature:dashboard`.

### ⚙️ Build
- `shared/build.gradle.kts` now includes `compose.materialIconsExtended`, `kotlinx.datetime`, and `:core` dependencies.


## [Unreleased] - 2025-07-28

### ✨ Features

- **OCR highlighting persistence**:
  - Added support for saving and loading OCR highlighting data (bounding boxes and text lines) for products.
  - The app can now reconstruct the exact text areas used during OCR, enabling future features like text highlighting on receipt images.

### 🛠 Refactoring / Improvements

- Moved `MyBoundingBox`, `MyTextBlock`, and `GroupedTextLine` models to `core/domain/model`.
- `Product` domain model now includes an optional `ocrGroupedTextLine` field.
- `ProductEntity.toDomainModel()` updated to accept a `GroupedTextLine`.

### 🗄 Database Changes

- Schema version bumped to **4**.
- Added new entities (`MyBoundingBoxEntity`, `MyTextBlockEntity`, `GroupedTextLineEntity`) and their DAOs.
- `HighlightingService` handles persistence and retrieval of OCR highlighting data.
- Removed legacy `Migration.kt` (MIGRATION_1_2); migrations now rely on version bumps.

### 🔄 Repository & Data Layer

- `ReceiptRepositoryImpl`:
  - Saves and loads OCR highlighting when saving or fetching receipts.
  - Deletes previous highlighting data for products on updates.

### 🎨 UI / Feature Updates

- Updated `ReceiptManager`, `TextRecognizer`, and related components to integrate OCR highlighting.
- UI elements (`ProductCard`, `AddReceiptContent`, etc.) now support highlighting-aware data.


## [Unreleased] - 2025-07-27
### ✨ Features
- Add dashboard feature module. Include this new module in feature home and navigation modules

## [Unreleased] - 2025-07-27

### ♻️ Refactoring
- **ColorPalette:** Updated default colors for Needs, Fun, and Limit to more distinct, stronger variants.
- **DatabaseInitializer:** Adjusted `getDefaultTypes()` and `getDefaultCategories()` to use new color values.
- **ReceiptListViewModel:** Fixed sum calculation to use `product.totalInCents` directly (no redundant multiplication by `qty`).

### 🔧 Other Changes
- **AndroidManifest:** Disabled app backup by setting `android:allowBackup="false"`.


## [Unreleased] - 2025-07-27

### ✨ Features
- **AddReceiptScreen:** Added ability to expand or collapse all product cards at once with a single button.
- **ProductCard:** Now controlled externally via `isExpanded` and `onExpandedChange` props.

### 🛠 Improvements
- Expansion states (`expandedStates`) are now centrally managed in `AddReceiptContent`.
- Expand/collapse all button is animated for better UX.


## [Unreleased] - 2025-07-20
### ✨ Features

- **Receipt editing**: Users can now edit existing receipts from the receipt list screen.
- **Edit navigation flow**: Added new route and navigation handling for editing receipts via `Screen.EditReceipt(receiptId)`.

### 🛠 Refactoring / Improvements

- **UI state expanded**: `AddReceiptUiState` and screen logic now differentiate between create and edit modes.
- **DAO behavior**: `ProductDao.getProductsByReceiptId` no longer sorts products by name, preserving original order.
- **Dynamic UI text**: Form titles and button labels adapt based on mode (e.g., "Zapisz" vs "Aktualizuj").

### 🧭 Navigation & DI

- **Navigation**:
  - `HomeGraphScreen` handles routing to edit mode.
  - `NavGraph.kt` contains commented-out alternative EditReceipt composable.
- **Dependency Injection**:
  - Registered `UpdateReceiptUseCase` in Koin.
  - Updated `ReceiptManager` with use cases needed for editing.


## [Unreleased] - 2025-07-20
### ✨ Features

- **Date filtering added**: Receipt list screen now supports predefined and custom date filtering via a new UI component and logic layer.
- **New component**: `DateFilterComponent` allows users to choose date filters like "Today", "This week", or select a custom range.

### 🛠 Refactoring / Architecture

- **Centralized date handling**: Introduced `DateUtils` with formatting, conversion, and range utilities in `core`.
- **Database date storage changed**: Refactored `ProductEntity` and `ReceiptEntity` to store `Long` timestamps instead of `String` dates.
- **ViewModel cleanup**: Removed manual date formatting from `ReceiptListViewModel`, now delegated to `DateUtils`.

### 🔧 Configuration

- **Database schema version bumped to v3** due to date field changes.
- **Dependencies**:
  - Added `kotlinx-datetime` to `core` and `data` modules.
  - Changed OpenAI model to `"gpt-4o"` in request builder.


## [Unreleased] - 2025-07-20
### ✨ Features

- **Receipt management added**: Introduced saving receipts with products to database and displaying them in a list.
- **New feature module**: `feature/receiptlist` with `ReceiptListScreen` and segmented receipt progress bar UI.
- **Use cases**: `SaveReceiptUseCase`, `GetReceiptsUseCase`, and `GetProductsUseCase` added for domain logic.

### 🛠 Refactoring / Architecture

- **Clean separation of layers**: Data layer updated to use Room entities and DAOs. Domain logic encapsulated in use cases.
- **Product and receipt linking**: `receiptId` now binds products to receipts.
- **Color management updated**: `ColorPalette` refactored to return Compose `Color` objects directly.

### 🔧 Configuration

- Integrated `receiptlist` module into navigation and DI (Koin).
- Added necessary Compose and Ktor dependencies to new modules.
- Reintroduced temporary Compose dependency to `data` layer due to color conversion issues (marked as TODO).

## [Unreleased] - 19.07.2025
### Refactored

- Use `Long` for color values in the data layer to avoid direct dependency on Compose UI.
- Added extension to convert `List<Long>` to Compose `Color` list in the shared module.
- Updated `DatabaseInitializer` to use `Long` color values.
- Modified `CategoriesViewModel` to initialize and convert colors properly for UI.
- Preserved `categories` and `types` state in `UiStateManager.clearState()` in add receipt feature.
- Cleaned up Gradle build files by removing unnecessary Compose dependencies from the data module and confirming dependencies in feature/home.

## [Unreleased] - 19.07.2025
### Refactored

- **AI Receipt Parsing aligned with Clean Architecture**
    - Introduced `OpenAIService` and `AIRepository` interfaces in the `core` module.
    - Added domain models for AI communication (`OpenAiRequest`, `OpenAiResponse`, `ParsedReceiptData`, `ParsedProductData`, etc.).
    - Implemented `ParseReceiptUseCase` to encapsulate receipt parsing logic.
    - Added custom exceptions for AI-related errors (`ParseReceiptException`, `NetworkException`, etc.).
    - `data` module: Added `OpenAIServiceImpl`, `AIRepositoryImpl`, `OpenAIRequestBuilder`, and `OpenAIMapper`.
    - Updated `OpenAiManager` in `feature/addreceipt` to use `ParseReceiptUseCase`.
    - Koin modules updated to provide all new AI-related services and clients.
    - Added long-timeout `openAIHttpClient` for OpenAI API calls.
    - Minor build config updates (e.g., Compose `Color` TODO in `DatabaseInitializer.kt`).

- **Core Module introduced for domain logic**
    - Added new `core` module to centralize domain models and interfaces.
    - Migrated models: `Category`, `Type`, `Product`, `Receipt`, and `CategoryType`.
    - Moved repository interfaces (`CategoriesTypesRepository`, `ReceiptRepository`) to `core`.
    - Relocated `GetTypesAndCategoriesUseCase` from `feature/addreceipt` to `core`.
    - Updated dependencies in `data`, `di`, `feature/addreceipt`, and `feature/categories` to use `core`.
    - Removed obsolete `CategoriesTypesImpl.kt`.
    - Adjusted imports and usage across ViewModels, managers, and components.
    - Updated build files and `settings.gradle.kts` to include `core`.

- **OpenAIService now receives injected Categories/Types**
    - Injected `CategoriesTypesRepository` into `OpenAiService` to dynamically fetch categories and types.
    - Changed model from `gpt-4o-2024-08-06` to `o4-mini`.
    - Updated OpenAI prompt format to use semicolons as separators.
    - Updated Koin bindings for new repository injection.
