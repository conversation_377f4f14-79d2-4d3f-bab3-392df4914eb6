package org.example.addmultiple.presentation

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import kotlinx.coroutines.delay

class AddMultipleViewModel : ViewModel() {
    
    private val _uiState = MutableStateFlow(AddMultipleUiState())
    val uiState: StateFlow<AddMultipleUiState> = _uiState.asStateFlow()
    
    fun onEvent(event: AddMultipleEvent) {
        when (event) {
            is AddMultipleEvent.StartScanner -> startScanning()
            is AddMultipleEvent.BulkUpload -> handleBulkUpload()
            is AddMultipleEvent.FromGallery -> handleFromGallery()
            is AddMultipleEvent.BackToEmpty -> backToEmpty()
            is AddMultipleEvent.EditReceipt -> editReceipt(event.receiptId)
            is AddMultipleEvent.RetryReceipt -> retryReceipt(event.receiptId)
            is AddMultipleEvent.DeleteReceipt -> deleteReceipt(event.receiptId)
            is AddMultipleEvent.AddMore -> addMoreReceipts()
            is AddMultipleEvent.ExportData -> exportData()
        }
    }
    
    private fun startScanning() {
        val sampleReceipts = listOf(
            ReceiptProcessingItem("1", "Dino", "123,63", 6, "12.03.2024", ProcessingStatus.Processed),
            ReceiptProcessingItem("2", "Dino", "123,63", 6, "12.03.2024", ProcessingStatus.Processed),
            ReceiptProcessingItem("3", "Dino", "123,63", 6, "12.03.2024", ProcessingStatus.Processed),
            ReceiptProcessingItem("4", "Dino", "123,63", 6, "12.03.2024", ProcessingStatus.Processed),
            ReceiptProcessingItem("5", "Dino", "123,63", 6, "12.03.2024", ProcessingStatus.Processing)
        )
        
        _uiState.value = _uiState.value.copy(
            screenState = MultipleReceiptScreenState.Processing(
                receipts = sampleReceipts,
                overallProgress = 0.8f,
                processedCount = 4,
                totalCount = 5
            )
        )
    }
    
    private fun handleBulkUpload() {
        // TODO: Implement bulk upload functionality
        println("Bulk upload requested")
    }
    
    private fun handleFromGallery() {
        // TODO: Implement gallery selection functionality
        println("From gallery requested")
    }
    
    private fun backToEmpty() {
        _uiState.value = _uiState.value.copy(
            screenState = MultipleReceiptScreenState.Empty
        )
    }
    
    private fun editReceipt(receiptId: String) {
        // TODO: Navigate to edit receipt screen
        println("Edit receipt: $receiptId")
    }
    
    private fun retryReceipt(receiptId: String) {
        viewModelScope.launch {
            val currentState = _uiState.value.screenState
            if (currentState is MultipleReceiptScreenState.Processing) {
                val updatedReceipts = currentState.receipts.map { receipt ->
                    if (receipt.id == receiptId) {
                        receipt.copy(status = ProcessingStatus.Processing)
                    } else {
                        receipt
                    }
                }
                
                _uiState.value = _uiState.value.copy(
                    screenState = currentState.copy(receipts = updatedReceipts)
                )
                
                // Simulate processing delay
                delay(2000)
                
                val finalReceipts = updatedReceipts.map { receipt ->
                    if (receipt.id == receiptId) {
                        receipt.copy(status = ProcessingStatus.Processed)
                    } else {
                        receipt
                    }
                }
                
                _uiState.value = _uiState.value.copy(
                    screenState = currentState.copy(receipts = finalReceipts)
                )
            }
        }
    }
    
    private fun deleteReceipt(receiptId: String) {
        val currentState = _uiState.value.screenState
        if (currentState is MultipleReceiptScreenState.Processing) {
            val updatedReceipts = currentState.receipts.filter { it.id != receiptId }
            val processedCount = updatedReceipts.count { it.status is ProcessingStatus.Processed }
            val totalCount = updatedReceipts.size
            val progress = if (totalCount > 0) processedCount.toFloat() / totalCount else 0f
            
            _uiState.value = _uiState.value.copy(
                screenState = currentState.copy(
                    receipts = updatedReceipts,
                    processedCount = processedCount,
                    totalCount = totalCount,
                    overallProgress = progress
                )
            )
        }
    }
    
    private fun addMoreReceipts() {
        // TODO: Implement add more receipts functionality
        println("Add more receipts requested")
    }
    
    private fun exportData() {
        // TODO: Implement export data functionality
        println("Export data requested")
    }
}

data class AddMultipleUiState(
    val screenState: MultipleReceiptScreenState = MultipleReceiptScreenState.Empty,
    val statsData: StatsData = StatsData()
)
