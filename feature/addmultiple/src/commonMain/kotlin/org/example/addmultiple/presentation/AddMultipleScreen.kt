package org.example.addmultiple.presentation

import androidx.compose.animation.*
import androidx.compose.animation.core.*
import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.rounded.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.scale
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.semantics.*
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import org.example.shared.ColorPalette
import org.koin.compose.viewmodel.koinViewModel

// Data Models
data class ReceiptProcessingItem(
    val id: String,
    val store: String,
    val amount: String,
    val products: Int,
    val date: String,
    val status: ProcessingStatus
)

sealed class ProcessingStatus {
    object Processing : ProcessingStatus()
    object Processed : ProcessingStatus()
    data class Error(val message: String) : ProcessingStatus()
}

sealed class MultipleReceiptScreenState {
    object Empty : MultipleReceiptScreenState()
    data class Processing(
        val receipts: List<ReceiptProcessingItem>,
        val overallProgress: Float,
        val processedCount: Int,
        val totalCount: Int
    ) : MultipleReceiptScreenState()
}

data class StatsData(
    val receiptsScanned: Int = 247,
    val receiptsChange: String = "+12%",
    val accuracyRate: String = "98.2%",
    val accuracyChange: String = "+8%"
)

// Events
sealed class AddMultipleEvent {
    object StartScanner : AddMultipleEvent()
    object BulkUpload : AddMultipleEvent()
    object FromGallery : AddMultipleEvent()
    object BackToEmpty : AddMultipleEvent()
    data class EditReceipt(val receiptId: String) : AddMultipleEvent()
    data class RetryReceipt(val receiptId: String) : AddMultipleEvent()
    data class DeleteReceipt(val receiptId: String) : AddMultipleEvent()
    object AddMore : AddMultipleEvent()
    object ExportData : AddMultipleEvent()
}

@Composable
fun AddMultipleReceiptsScreen(
    navigateBack: () -> Unit = {}
) {
    val viewModel = koinViewModel<AddMultipleViewModel>()
    val uiState by viewModel.uiState.collectAsState()

    // Performance optimization: memoize the event handler
    val onEvent = remember(viewModel) { viewModel::onEvent }

    // Performance optimization: derive screen type to avoid unnecessary recompositions
    val isProcessingScreen by remember {
        derivedStateOf { uiState.screenState is MultipleReceiptScreenState.Processing }
    }

    val currentScreenState = uiState.screenState
    when (currentScreenState) {
        is MultipleReceiptScreenState.Empty -> {
            EmptyScreen(
                statsData = uiState.statsData,
                onEvent = onEvent
            )
        }
        is MultipleReceiptScreenState.Processing -> {
            ProcessingScreen(
                state = currentScreenState,
                onEvent = onEvent,
                navigateBack = navigateBack
            )
        }
    }
}

@Composable
private fun EmptyScreen(
    statsData: StatsData,
    onEvent: (AddMultipleEvent) -> Unit
) {
    val gradientBackground = Brush.linearGradient(
        colors = listOf(
            Color(0xFFEFF6FF), // blue-50
            Color(0xFFEDE9FE), // indigo-50
            Color(0xFFFAF5FF)  // purple-50
        )
    )

    Column(
        modifier = Modifier
            .fillMaxSize()
            .background(gradientBackground)
            .padding(24.dp)
    ) {
        Spacer(modifier = Modifier.height(48.dp))

        // Header
        Column(
            modifier = Modifier.padding(bottom = 32.dp)
        ) {
            Text(
                text = "Receipt Scanner",
                style = MaterialTheme.typography.headlineLarge.copy(
                    fontWeight = FontWeight.Bold,
                    color = MaterialTheme.colorScheme.onSurface
                ),
                modifier = Modifier.padding(bottom = 8.dp)
            )
            Text(
                text = "Digitize your receipts instantly",
                style = MaterialTheme.typography.bodyLarge.copy(
                    color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f)
                )
            )
        }

        // Main Action Cards
        Column(
            modifier = Modifier.padding(bottom = 32.dp),
            verticalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            ActionCard(
                title = "Start Scanner",
                subtitle = "Capture or choose receipt images from phone",
                icon = Icons.Rounded.CameraAlt,
                gradientColors = listOf(ColorPalette.Blue4, ColorPalette.Blue5),
                onClick = { onEvent(AddMultipleEvent.StartScanner) }
            )

            ActionCard(
                title = "Bulk Upload",
                subtitle = "Start scanning multiple receipts at once",
                icon = Icons.Rounded.Upload,
                gradientColors = listOf(ColorPalette.Violet4, ColorPalette.Violet5),
                onClick = { onEvent(AddMultipleEvent.BulkUpload) }
            )

            ActionCard(
                title = "From Gallery",
                subtitle = "Select multiple receipts from gallery",
                icon = Icons.Rounded.Image,
                gradientColors = listOf(ColorPalette.Green4, ColorPalette.Green5),
                onClick = { onEvent(AddMultipleEvent.FromGallery) }
            )
        }

        // Stats Cards
        StatsSection(statsData = statsData)
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun ActionCard(
    title: String,
    subtitle: String,
    icon: ImageVector,
    gradientColors: List<Color>,
    onClick: () -> Unit
) {
    var isPressed by remember { mutableStateOf(false) }

    val scale by animateFloatAsState(
        targetValue = if (isPressed) 0.95f else 1f,
        animationSpec = spring(
            dampingRatio = Spring.DampingRatioMediumBouncy,
            stiffness = Spring.StiffnessLow
        ),
        label = "cardScale"
    )

    val elevation by animateDpAsState(
        targetValue = if (isPressed) 12.dp else 8.dp,
        animationSpec = spring(
            dampingRatio = Spring.DampingRatioMediumBouncy,
            stiffness = Spring.StiffnessMedium
        ),
        label = "cardElevation"
    )

    // Reset pressed state after animation
    LaunchedEffect(isPressed) {
        if (isPressed) {
            kotlinx.coroutines.delay(150)
            isPressed = false
        }
    }

    Card(
        onClick = {
            isPressed = true
            onClick()
        },
        modifier = Modifier
            .fillMaxWidth()
            .scale(scale)
            .semantics {
                contentDescription = "$title - $subtitle"
                role = Role.Button
            },
        shape = RoundedCornerShape(16.dp),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.surface
        ),
        elevation = CardDefaults.cardElevation(
            defaultElevation = elevation
        )
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(24.dp),
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            // Icon with gradient background and animation
            val iconScale by animateFloatAsState(
                targetValue = if (isPressed) 1.1f else 1f,
                animationSpec = spring(
                    dampingRatio = Spring.DampingRatioMediumBouncy,
                    stiffness = Spring.StiffnessMedium
                ),
                label = "iconScale"
            )

            Box(
                modifier = Modifier
                    .size(56.dp)
                    .clip(RoundedCornerShape(12.dp))
                    .background(
                        Brush.linearGradient(gradientColors)
                    ),
                contentAlignment = Alignment.Center
            ) {
                Icon(
                    imageVector = icon,
                    contentDescription = title,
                    tint = Color.White,
                    modifier = Modifier
                        .size(24.dp)
                        .scale(iconScale)
                )
            }

            // Text content
            Column(
                modifier = Modifier.weight(1f)
            ) {
                Text(
                    text = title,
                    style = MaterialTheme.typography.titleMedium.copy(
                        fontWeight = FontWeight.SemiBold,
                        color = MaterialTheme.colorScheme.onSurface
                    ),
                    modifier = Modifier.padding(bottom = 4.dp)
                )
                Text(
                    text = subtitle,
                    style = MaterialTheme.typography.bodyMedium.copy(
                        color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f)
                    )
                )
            }
        }
    }
}

@Composable
private fun StatsSection(
    statsData: StatsData
) {

    Row(
        modifier = Modifier.fillMaxWidth(),
        horizontalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        StatsCard(
            modifier = Modifier.weight(1f),
            icon = Icons.Rounded.Upload,
            iconColor = ColorPalette.Blue5,
            iconBackgroundColor = ColorPalette.Blue1,
            value = statsData.receiptsScanned.toString(),
            label = "Receipts scanned",
            changeIndicator = statsData.receiptsChange
        )

        StatsCard(
            modifier = Modifier.weight(1f),
            icon = Icons.Rounded.Check,
            iconColor = ColorPalette.Violet5,
            iconBackgroundColor = ColorPalette.Violet1,
            value = statsData.accuracyRate,
            label = "Accuracy rate",
            changeIndicator = statsData.accuracyChange
        )
    }
}

@Composable
private fun StatsCard(
    modifier: Modifier = Modifier,
    icon: ImageVector,
    iconColor: Color,
    iconBackgroundColor: Color,
    value: String,
    label: String,
    changeIndicator: String
) {
    Card(
        modifier = modifier,
        shape = RoundedCornerShape(16.dp),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.surface
        ),
        elevation = CardDefaults.cardElevation(defaultElevation = 8.dp)
    ) {
        Column(
            modifier = Modifier.padding(20.dp)
        ) {
            // Header with icon and change indicator
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(bottom = 12.dp),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Box(
                    modifier = Modifier
                        .size(40.dp)
                        .clip(RoundedCornerShape(8.dp))
                        .background(iconBackgroundColor),
                    contentAlignment = Alignment.Center
                ) {
                    Icon(
                        imageVector = icon,
                        contentDescription = label,
                        tint = iconColor,
                        modifier = Modifier.size(20.dp)
                    )
                }

                Surface(
                    shape = RoundedCornerShape(12.dp),
                    color = ColorPalette.Green1,
                    modifier = Modifier.padding(start = 8.dp)
                ) {
                    Text(
                        text = changeIndicator,
                        style = MaterialTheme.typography.labelSmall.copy(
                            fontWeight = FontWeight.Medium,
                            color = ColorPalette.Green5
                        ),
                        modifier = Modifier.padding(horizontal = 8.dp, vertical = 4.dp)
                    )
                }
            }

            // Value
            Text(
                text = value,
                style = MaterialTheme.typography.headlineMedium.copy(
                    fontWeight = FontWeight.Bold,
                    color = MaterialTheme.colorScheme.onSurface
                ),
                modifier = Modifier.padding(bottom = 4.dp)
            )

            // Label
            Text(
                text = label,
                style = MaterialTheme.typography.bodyMedium.copy(
                    color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f)
                )
            )
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun ProcessingScreen(
    state: MultipleReceiptScreenState.Processing,
    onEvent: (AddMultipleEvent) -> Unit,
    navigateBack: () -> Unit
) {
    val gradientBackground = Brush.linearGradient(
        colors = listOf(
            Color(0xFFEFF6FF), // blue-50
            Color(0xFFEDE9FE), // indigo-50
            Color(0xFFFAF5FF)  // purple-50
        )
    )

    Box(modifier = Modifier.fillMaxSize()) {
        Column(
            modifier = Modifier
                .fillMaxSize()
                .background(gradientBackground)
                .padding(24.dp)
        ) {
            Spacer(modifier = Modifier.height(48.dp))

            // Header with back button
            Column(
                modifier = Modifier.padding(bottom = 32.dp)
            ) {
                TextButton(
                    onClick = { onEvent(AddMultipleEvent.BackToEmpty) },
                    modifier = Modifier.padding(bottom = 16.dp)
                ) {
                    Icon(
                        imageVector = Icons.Rounded.ArrowBack,
                        contentDescription = "Back",
                        modifier = Modifier.size(16.dp)
                    )
                    Spacer(modifier = Modifier.width(4.dp))
                    Text("Back")
                }

                Text(
                    text = "Processing Receipts",
                    style = MaterialTheme.typography.headlineMedium.copy(
                        fontWeight = FontWeight.Bold,
                        color = MaterialTheme.colorScheme.onSurface
                    ),
                    modifier = Modifier.padding(bottom = 4.dp)
                )
                Text(
                    text = "${state.processedCount}/${state.totalCount} receipts processed",
                    style = MaterialTheme.typography.bodyLarge.copy(
                        color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f)
                    )
                )
            }

            // Progress Bar Card
            ProgressCard(
                progress = state.overallProgress,
                modifier = Modifier.padding(bottom = 24.dp)
            )

            // Receipt List with animations
            LazyColumn(
                modifier = Modifier.weight(1f),
                verticalArrangement = Arrangement.spacedBy(16.dp)
            ) {
                items(
                    items = state.receipts,
                    key = { it.id }
                ) { receipt ->
                    AnimatedVisibility(
                        visible = true,
                        enter = slideInVertically(
                            animationSpec = spring(
                                dampingRatio = Spring.DampingRatioMediumBouncy,
                                stiffness = Spring.StiffnessMedium
                            )
                        ) + fadeIn(
                            animationSpec = tween(300)
                        ),
                        exit = slideOutVertically(
                            animationSpec = spring(
                                dampingRatio = Spring.DampingRatioMediumBouncy,
                                stiffness = Spring.StiffnessMedium
                            )
                        ) + fadeOut(
                            animationSpec = tween(300)
                        )
                    ) {
                        ReceiptProcessingCard(
                            receipt = receipt,
                            onEdit = { onEvent(AddMultipleEvent.EditReceipt(receipt.id)) },
                            onRetry = { onEvent(AddMultipleEvent.RetryReceipt(receipt.id)) },
                            onDelete = { onEvent(AddMultipleEvent.DeleteReceipt(receipt.id)) }
                        )
                    }
                }
            }

            Spacer(modifier = Modifier.height(80.dp)) // Space for bottom actions
        }

        // Fixed Bottom Actions
        BottomActionBar(
            modifier = Modifier.align(Alignment.BottomCenter),
            onAddMore = { onEvent(AddMultipleEvent.AddMore) },
            onExportData = { onEvent(AddMultipleEvent.ExportData) }
        )
    }
}

@Composable
private fun ProgressCard(
    progress: Float,
    modifier: Modifier = Modifier
) {
    val animatedProgress by animateFloatAsState(
        targetValue = progress,
        animationSpec = tween(
            durationMillis = 1000,
            easing = EaseOutCubic
        ),
        label = "progressAnimation"
    )

    Card(
        modifier = modifier.fillMaxWidth(),
        shape = RoundedCornerShape(16.dp),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.surface
        ),
        elevation = CardDefaults.cardElevation(defaultElevation = 8.dp)
    ) {
        Column(
            modifier = Modifier.padding(24.dp)
        ) {
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(bottom = 12.dp),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = "Overall Progress",
                    style = MaterialTheme.typography.titleSmall.copy(
                        fontWeight = FontWeight.Medium,
                        color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.8f)
                    )
                )

                AnimatedContent(
                    targetState = (animatedProgress * 100).toInt(),
                    transitionSpec = {
                        slideInVertically { height -> height } + fadeIn() togetherWith
                        slideOutVertically { height -> -height } + fadeOut()
                    },
                    label = "percentageAnimation"
                ) { percentage ->
                    Text(
                        text = "$percentage%",
                        style = MaterialTheme.typography.titleSmall.copy(
                            fontWeight = FontWeight.Medium,
                            color = ColorPalette.Blue5
                        )
                    )
                }
            }

            LinearProgressIndicator(
                progress = { animatedProgress },
                modifier = Modifier
                    .fillMaxWidth()
                    .height(12.dp)
                    .clip(RoundedCornerShape(6.dp))
                    .semantics {
                        contentDescription = "Overall progress: ${(animatedProgress * 100).toInt()} percent complete"
                        progressBarRangeInfo = ProgressBarRangeInfo(
                            current = animatedProgress,
                            range = 0f..1f
                        )
                    },
                color = ColorPalette.Blue5,
                trackColor = MaterialTheme.colorScheme.surfaceVariant,
            )
        }
    }
}

@Composable
private fun ReceiptProcessingCard(
    receipt: ReceiptProcessingItem,
    onEdit: () -> Unit,
    onRetry: () -> Unit,
    onDelete: () -> Unit
) {
    // Performance optimization: memoize status description
    val statusDescription = remember(receipt.status) {
        when (receipt.status) {
            is ProcessingStatus.Processed -> "Receipt processed successfully"
            is ProcessingStatus.Processing -> "Receipt is currently being processed"
            is ProcessingStatus.Error -> "Receipt processing failed: ${receipt.status.message}"
        }
    }

    Card(
        modifier = Modifier
            .fillMaxWidth()
            .semantics {
                contentDescription = "Receipt from ${receipt.store}, amount ${receipt.amount} PLN, ${receipt.products} products, date ${receipt.date}. $statusDescription"
            },
        shape = RoundedCornerShape(16.dp),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.surface
        ),
        elevation = CardDefaults.cardElevation(defaultElevation = 8.dp)
    ) {
        Column(
            modifier = Modifier.padding(20.dp)
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.spacedBy(16.dp)
            ) {
                // Thumbnail placeholder
                Box(
                    modifier = Modifier
                        .size(64.dp)
                        .clip(RoundedCornerShape(12.dp))
                        .background(
                            Brush.linearGradient(
                                listOf(MaterialTheme.colorScheme.surfaceVariant,
                                       MaterialTheme.colorScheme.surfaceVariant.copy(alpha = 0.7f))
                            )
                        ),
                    contentAlignment = Alignment.Center
                ) {
                    Text(
                        text = "IMG",
                        style = MaterialTheme.typography.labelMedium.copy(
                            fontWeight = FontWeight.SemiBold,
                            color = MaterialTheme.colorScheme.onSurfaceVariant
                        )
                    )
                }

                // Receipt Info
                Column(
                    modifier = Modifier.weight(1f)
                ) {
                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(bottom = 8.dp),
                        horizontalArrangement = Arrangement.SpaceBetween,
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Text(
                            text = receipt.store,
                            style = MaterialTheme.typography.titleMedium.copy(
                                fontWeight = FontWeight.SemiBold,
                                color = MaterialTheme.colorScheme.onSurface
                            )
                        )
                        Text(
                            text = "${receipt.amount} PLN",
                            style = MaterialTheme.typography.titleMedium.copy(
                                fontWeight = FontWeight.Bold,
                                color = MaterialTheme.colorScheme.onSurface
                            )
                        )
                    }

                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.SpaceBetween
                    ) {
                        Text(
                            text = "${receipt.products} produktów",
                            style = MaterialTheme.typography.bodyMedium.copy(
                                color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f)
                            )
                        )
                        Text(
                            text = receipt.date,
                            style = MaterialTheme.typography.bodyMedium.copy(
                                color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f)
                            )
                        )
                    }
                }
            }

            // Action Buttons Row
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(top = 16.dp),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                // Status indicator
                StatusIndicator(status = receipt.status)

                // Action buttons
                Row(
                    horizontalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    when (receipt.status) {
                        is ProcessingStatus.Processed -> {
                            IconButton(
                                onClick = onEdit,
                                modifier = Modifier.semantics {
                                    contentDescription = "Edit receipt from ${receipt.store}"
                                    role = Role.Button
                                }
                            ) {
                                Icon(
                                    imageVector = Icons.Rounded.Edit,
                                    contentDescription = null, // Handled by button semantics
                                    tint = ColorPalette.Blue5,
                                    modifier = Modifier.size(20.dp)
                                )
                            }
                            IconButton(
                                onClick = onRetry,
                                modifier = Modifier.semantics {
                                    contentDescription = "Retry processing receipt from ${receipt.store}"
                                    role = Role.Button
                                }
                            ) {
                                Icon(
                                    imageVector = Icons.Rounded.Refresh,
                                    contentDescription = null, // Handled by button semantics
                                    tint = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f),
                                    modifier = Modifier.size(20.dp)
                                )
                            }
                        }
                        is ProcessingStatus.Error -> {
                            Button(
                                onClick = onRetry,
                                colors = ButtonDefaults.buttonColors(
                                    containerColor = ColorPalette.Red1,
                                    contentColor = ColorPalette.Red5
                                ),
                                shape = RoundedCornerShape(8.dp)
                            ) {
                                Text(
                                    text = "Try again",
                                    style = MaterialTheme.typography.labelSmall.copy(
                                        fontWeight = FontWeight.Medium
                                    )
                                )
                            }
                        }
                        is ProcessingStatus.Processing -> {
                            // No action buttons during processing
                        }
                    }

                    IconButton(
                        onClick = onDelete,
                        modifier = Modifier.semantics {
                            contentDescription = "Delete receipt from ${receipt.store}"
                            role = Role.Button
                        }
                    ) {
                        Icon(
                            imageVector = Icons.Rounded.Delete,
                            contentDescription = null, // Handled by button semantics
                            tint = ColorPalette.Red5,
                            modifier = Modifier.size(20.dp)
                        )
                    }
                }
            }
        }
    }
}

@Composable
private fun StatusIndicator(
    status: ProcessingStatus
) {
    Row(
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.spacedBy(8.dp)
    ) {
        when (status) {
            is ProcessingStatus.Processed -> {
                Icon(
                    imageVector = Icons.Rounded.Check,
                    contentDescription = "Processed",
                    tint = ColorPalette.Green5,
                    modifier = Modifier.size(16.dp)
                )
                Text(
                    text = "Processed",
                    style = MaterialTheme.typography.labelSmall.copy(
                        fontWeight = FontWeight.Medium,
                        color = ColorPalette.Green5
                    )
                )
            }
            is ProcessingStatus.Processing -> {
                val infiniteTransition = rememberInfiniteTransition(label = "processingAnimation")
                val alpha by infiniteTransition.animateFloat(
                    initialValue = 0.3f,
                    targetValue = 1f,
                    animationSpec = infiniteRepeatable(
                        animation = tween(1000, easing = EaseInOutSine),
                        repeatMode = RepeatMode.Reverse
                    ),
                    label = "alphaAnimation"
                )

                CircularProgressIndicator(
                    modifier = Modifier.size(16.dp),
                    strokeWidth = 2.dp,
                    color = ColorPalette.Blue5.copy(alpha = alpha)
                )
                Text(
                    text = "Processing...",
                    style = MaterialTheme.typography.labelSmall.copy(
                        fontWeight = FontWeight.Medium,
                        color = ColorPalette.Blue5.copy(alpha = alpha)
                    )
                )
            }
            is ProcessingStatus.Error -> {
                Icon(
                    imageVector = Icons.Rounded.Error,
                    contentDescription = "Error",
                    tint = ColorPalette.Red5,
                    modifier = Modifier.size(16.dp)
                )
                Text(
                    text = "Error",
                    style = MaterialTheme.typography.labelSmall.copy(
                        fontWeight = FontWeight.Medium,
                        color = ColorPalette.Red5
                    )
                )
            }
        }
    }
}

@Composable
private fun BottomActionBar(
    modifier: Modifier = Modifier,
    onAddMore: () -> Unit,
    onExportData: () -> Unit
) {
    Surface(
        modifier = modifier
            .fillMaxWidth()
            .padding(24.dp),
        shape = RoundedCornerShape(16.dp),
        shadowElevation = 8.dp
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            horizontalArrangement = Arrangement.spacedBy(12.dp)
        ) {
            OutlinedButton(
                onClick = onAddMore,
                modifier = Modifier.weight(1f),
                shape = RoundedCornerShape(16.dp),
                border = BorderStroke(2.dp, MaterialTheme.colorScheme.outline),
                colors = ButtonDefaults.outlinedButtonColors(
                    contentColor = MaterialTheme.colorScheme.onSurface
                )
            ) {
                Text(
                    text = "Add More",
                    style = MaterialTheme.typography.titleSmall.copy(
                        fontWeight = FontWeight.SemiBold
                    ),
                    modifier = Modifier.padding(vertical = 8.dp)
                )
            }

            Button(
                onClick = onExportData,
                modifier = Modifier.weight(1f),
                shape = RoundedCornerShape(16.dp),
                colors = ButtonDefaults.buttonColors(
                    containerColor = ColorPalette.Blue5
                )
            ) {
                Text(
                    text = "Export Data",
                    style = MaterialTheme.typography.titleSmall.copy(
                        fontWeight = FontWeight.SemiBold,
                        color = Color.White
                    ),
                    modifier = Modifier.padding(vertical = 8.dp)
                )
            }
        }
    }
}

