package org.example.navigation

import androidx.compose.runtime.Composable
import androidx.navigation.compose.NavHost
import androidx.navigation.compose.composable
import androidx.navigation.compose.rememberNavController
import androidx.navigation.toRoute
import org.example.addReceipt.ui.AddReceiptScreen
import org.example.budget.BudgetScreen
import org.example.category.CategoriesScreen
import org.example.home.HomeGraphScreen
import org.example.shared.navigation.Screen

@Composable
fun SetupNavGraph(startDestination: Screen /* = Screen.HomeGraph For future auth*/) {
    val navController = rememberNavController()
    NavHost(
        navController = navController,
        startDestination = startDestination
    ) {
        composable<Screen.HomeGraph> {
            HomeGraphScreen(
                navigateToCategory = {
                    navController.navigate(Screen.Category)
                },
                navigateToBudget = {
                    navController.navigate(Screen.Budget)
                },
                navigateToAddReceipt = {
                    navController.navigate(Screen.AddReceipt)
                },
                navigateToAddReceiptWithScan = {
                    navController.navigate(Screen.AddReceiptWithScan)
                },
                navigateToEditReceipt = { receiptId ->
                    navController.navigate(Screen.EditReceipt(receiptId))
                },
                navigateToDuplicated = { receiptId ->
                    navController.navigate(Screen.AddReceiptDuplicated(receiptId))
                }
            )
        }
        composable<Screen.AddReceipt> {
            AddReceiptScreen(
                navigateBack = {
                    navController.navigateUp()
                }
            )
        }

        composable<Screen.AddReceiptWithScan> {
            AddReceiptScreen(
                navigateBack = {
                    navController.navigateUp()
                },
                startWithScan = true
            )
        }

        composable<Screen.AddReceiptDuplicated> { backStackEntry ->
            val duplicatedReceipt = backStackEntry.toRoute<Screen.AddReceiptDuplicated>()
            AddReceiptScreen(
                receiptId = duplicatedReceipt.receiptId,
                navigateBack = {
                    navController.navigateUp()
                }
            )
        }

        composable<Screen.EditReceipt> { backStackEntry ->
            val editReceipt = backStackEntry.toRoute<Screen.EditReceipt>()
            AddReceiptScreen(
                receiptId = editReceipt.receiptId,
                isEditMode = true,
                navigateBack = {
                    navController.navigateUp()
                },

                )
        }

        // Drawer screens
        composable<Screen.Category> {
            CategoriesScreen(
                navigateBack = {
                    navController.navigateUp()
                }
            )
        }

        composable<Screen.Budget> {
            BudgetScreen(
                navigateBack = {
                    navController.navigateUp()
                }
            )
        }
    }
}